<template>
  <div :class="getCurrentClass()">
    <div class="border-left " :class="getStyleNumClass()" />
    <div class="item" style="margin-left: 0px">
      <div class="before">
        <i class="icon" title="无编辑权限" />
      </div>
      <div class="switch cus-edit-toolbar-icon" :class="getExpandIconClass()" @click.stop="toggleExpand"></div>
      <div class="padding" style="width: 89px;">
        <div class="line" :class="getStyleNumClass()"></div>
        <!--        选择图标  -->
        <i
          v-if="showSelectedICon"
          class="cus-edit-toolbar-icon icon-edit-toolbarxuanze c-pointer-hover ml4" />
        <!--        世系  -->
        <span>[{{ node.generation }}世]</span>
      </div>
      <div class="name" :class="selectedKey === node.id ? 'selected' : ''">
        <!--        小人 图标 -->
        <i
          class="cus-edit-toolbar-icon"
          :class="getPersonIconClass(node.sex)"
          v-if="!noShowPersonIcon"
          style="font-size: 16px;" />
        <!--        姓氏 + 名称  -->
        <div v-show="node.writeMode === 2" class="text" @click="handleNodeClick">
          <button class="btn-name">
            <span>{{ node.first_name }}</span>
            <span>{{ node.name }}</span>
          </button>
        </div>
        <div v-if="!noAdd && node.writeMode === 1" class="text mode-1">
          <a-input size="small" v-model="childName" @pressEnter="handleSaveChildren" />
          <i
            class="cus-edit-toolbar-icon cus-edit-toolbar-icon icon-edit-toolbarbaocun c-pointer-hover mr6"
            title="保存该项"
            @click="handleSaveChildren" />
          <i
            class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover"
            @click="handleDelChildren"
            title="删除该项" />
        </div>
        <!--        排行    -->
        <span v-if="!noShowRank">[{{ node.ranking_text }}]</span>
        <!--        配偶    -->
        <div
          v-if="!noWife"
          v-for="(wife, wifeIndex) in (node?.wife || [])"
          class="name wife-item"
          :key="wife.id"
          :class="selectedKey === wife.id ? 'selected wife-selected' : ''"
          :style="{ marginTop: wifeIndex > 0 ? '4px' : '0' }"
          @click="handleWifeClick(wife)">
          <i class="cus-edit-toolbar-icon" :class="getPersonIconClass(wife.sex)" />
          <div class="wife-content">
            <span class="wife-text">{{ wife.full_name || '' }}</span>
          </div>
        </div>
        <div
          v-if="!noAdd && (selectedKey === node.id || (node.wife && node.wife.some(w => w.id === selectedKey)))"
          class="name">
          <i
            class="cus-edit-toolbar-icon"
            :class="getSpouseIconClass()"
            style="color: #f5abab; font-size: 14px;"
            v-show="isAddWife" />
          <a-input
            size="small"
            v-show="isAddWife"
            v-model.trim="wifeName"
            style="width: 80px;margin-right: 10px;"
            @pressEnter="handleSaveWife" />
          <i
            class="cus-edit-toolbar-icon icon-edit-toolbarbaocun c-pointer-hover mr6"
            title="保存该项"
            @click="handleSaveWife"
            v-show="isAddWife" />
          <i
            class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1 c-pointer-hover"
            @click="handleDelWife"
            title="删除该项"
            v-show="isAddWife" />
          <i
            class="cus-edit-toolbar-icon"
            :class="getSpouseIconClass()"
            style="color: #f5abab; font-size: 14px;"
            v-show="!isAddWife" />
          <div class="more-wife" @click="isAddWife = true" v-show="!isAddWife">添加配偶</div>
        </div>
        <div class="name-top" v-if="node.branch_tag">
          <span class="tag">{{ getTagName(node.branch_tag) }}</span>
          <span class="branch">[{{ node.branch_name }}]</span>
        </div>
      </div>
    </div>
    <div
      class="children"
      :class="selectedKey === node.id ? 'selected-node' : ''"
      v-if="expanded && node.children && node.children.length">
      <div style="margin-left: 0px;">
        <tree-node
          v-for="(nd, idx) in node.children"
          :key="nd.id"
          :node="nd"
          :checked-keys="checkedKeys"
          :expanded-keys="expandedKeys"
          :selected-key="selectedKey"
          :index="index + 1"
          :last="idx === node.children.length - 1"
          :fatherNode="node"
          :no-add="noAdd"
          :no-wife="noWife"
          :no-show-rank="noShowRank"
          :no-show-person-icon="noShowPersonIcon"
          @check="$listeners.check"
          @expand="$listeners.expand"
          @click="$listeners.click"
          @wifeClick="$listeners.wifeClick"
          @addBrother="$listeners.addBrother"
          @addChild="$listeners.addChild" />
      </div>
    </div>
    <div class="more-child" v-if="!noAdd && shouldShowAddChild" style="margin-left: 0px;">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarnance" />
      <div class="text" @click="handleAddChild">添加孩子</div>
    </div>
    <div class="more-child more-brother" v-if="!noAdd && shouldShowAddBrother" style="margin-left: 0px;">
      <i class="cus-edit-toolbar-icon icon-edit-toolbarnance" />
      <div class="text" @click="handleAddBrother">添加兄妹</div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import { getHandleName } from '@/utils/nameUtil'
import { getRankText } from '@/utils/rankTextUtil'
import * as actionTypes from '@/store/modules/px-action-type'
import { TagTypeMap } from '@/constant'

export default {
  name: 'TreeNode',
  inheritAttrs: false,
  props: {
    node: {
      type: Object,
      required: true
    },
    fatherNode: {
      type: Object,
      default: () => { }
    },
    expandedKeys: {
      type: Array,
      default: () => []
    },
    checkedKeys: {
      type: Array,
      default: () => []
    },
    index: {
      type: Number,
      default: 0
    },
    last: {
      type: Boolean,
      default: false
    },
    selectedKey: {
      type: [Number, String],
      default: -9999
    },
    showSelectedICon: {
      type: Boolean,
      default: false
    },
    noAdd: {
      type: Boolean,
      default: false
    },
    noWife: {
      type: Boolean,
      default: false
    },
    noShowRank: {
      type: Boolean,
      default: false
    },
    noShowPersonIcon: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      wifeName: '',
      isAddWife: false,
      childName: this.node.first_name,
      expanded: this.expandedKeys.includes(this.node.key),
      checked: false,
      hasBeenExpanded: false, // 记录节点是否曾经被展开过
      isLoadingChildren: false // 记录是否正在加载子节点数据
    }
  },
  computed: {
    ...mapState({
      quickOperationsType: state => state.px.quickOperationsType,
      currentPedigreeMemberList: state => state.px.currentPedigreeMemberList,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    }),
    shouldShowAddChild () {
      // 当选中主支成员时显示添加孩子按钮
      // 当选中配偶时也显示添加孩子按钮（配偶可以添加孩子）
      if (this.selectedKey === this.node.id) {
        // 选中的是主支成员
        return true
      }

      // 检查是否选中了该节点的配偶
      if (this.node.wife && this.node.wife.length > 0) {
        const selectedWife = this.node.wife.find(wife => wife.id === this.selectedKey)
        if (selectedWife) {
          // 选中的是配偶，显示添加孩子按钮
          return true
        }
      }

      return false
    },
    shouldShowAddBrother () {
      // 只有当选中主支成员时才显示添加兄妹按钮
      // 选中配偶时不显示添加兄妹按钮
      if (this.selectedKey === this.node.id) {
        // 选中的是主支成员，显示添加兄妹按钮
        return true
      }

      return false
    }
  },
  watch: {
    expandedKeys: {
      handler (newVal) {
        this.expanded = newVal.includes(this.node.key)
      },
      immediate: true
    },
    // 监听 checkedKeys 的变化，更新当前节点的选中状态
    checkedKeys: {
      handler (newVal) {
        this.checked = newVal.includes(this.node.key)
      },
      immediate: true
    },
    quickOperationsType: {
      handler (newVal) {
        if (newVal && this.node.id === this.selectedKey) {
          this.ResetQuickOperationsType()
          switch (newVal) {
            case actionTypes.PX_RIGHT_OPTION_ADD_BROTHER:
              this.handleAddBrother()
              break
            case actionTypes.PX_RIGHT_OPTION_ADD_CHILD:
              this.handleAddChild()
              break
            case actionTypes.PX_RIGHT_OPTION_ADD_WIFE:
              this.isAddWife = true
              break
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions([
      'AddMemberChild',
      'AddWife',
      'SetLoading',
      'ResetLoading',
      'RefreshPedigreeMemberList',
      'RefreshPedigreeMemberListKeepExpanded',
      'LoadAndAppendChildGenerations',
      'RefreshParentNodeChildren',
      'ResetQuickOperationsType'
    ]),
    // 切换展开/折叠 - 优化版本，支持无限异步加载
    async toggleExpand () {
      // 防止重复点击和无限循环
      if (this.isLoadingChildren) {
        return
      }

      // 如果当前是展开状态，需要检查是否真的有子节点数据
      if (this.expanded) {
        // 如果已展开但没有子节点数据，且has_child为1，应该加载数据而不是折叠
        if (this.node.has_child === 1 && (!this.node.children || this.node.children.length === 0)) {
          // 不要return，继续执行加载逻辑
        } else {
          // 有子节点数据的情况下才执行折叠
          this.expanded = false
          this.$emit('expand', this.node.key, this.expanded)
          return
        }
      }

      // 如果当前是折叠状态，要切换为展开
      // 检查是否需要异步加载子节点数据
      const needsLoading = this.shouldLoadChildren()

      if (needsLoading) {
        try {
          // 异步加载并拼接子世系数据，固定请求3个世系
          await this.loadAndAppendChildGenerations()
          this.hasBeenExpanded = true // 记录节点曾经被展开过
        } catch (error) {
          console.error('加载子节点数据失败:', error)
          // 根据错误类型显示不同的错误消息
          if (error.message && error.message.includes('Cannot read properties of undefined')) {
            this.$message.error('数据格式错误，请刷新页面后重试')
          } else {
            this.$message.error('加载子节点数据失败，请重试')
          }
          return // 加载失败时不展开节点
        }
      }

      this.expanded = true
      this.hasBeenExpanded = true // 记录节点曾经被展开过
      this.$emit('expand', this.node.key, this.expanded)
    },

    // 判断是否需要加载子节点数据 - 优化版本
    shouldLoadChildren () {
      // 如果正在加载，不需要重复加载
      if (this.isLoadingChildren) {
        return false
      }

      // 优先使用 has_child 字段判断是否有子节点
      // has_child 为 0 表示确定没有子节点，不需要加载
      if (this.node.has_child === 0) {
        return false
      }

      // has_child 为 1 表示有可加载的子世代
      if (this.node.has_child === 1) {
        // 如果没有子节点数据或子节点数据为空，需要加载
        if (!this.node.children || this.node.children.length === 0) {
          return true
        }

        // 如果已经有子节点数据，检查这些子节点是否有实际内容
        // 只有当子节点数组不为空且包含有效数据时，才认为不需要加载
        const hasValidChildren = this.node.children.length > 0 &&
          this.node.children.some(child => child.id !== undefined && child.id !== null)

        if (hasValidChildren) {
          // 已经有有效的子节点数据，不需要重复加载
          return false
        }

        // 如果子节点数组存在但为空或无效，仍需要加载
        return true
      }

      // has_child 为 null 或 undefined 的情况
      // 如果已经有子节点数据，不需要加载
      if (this.node.children && this.node.children.length > 0) {
        return false
      }

      // 如果节点从未被展开过，可能需要加载（保守策略）
      return !this.hasBeenExpanded
    },

    // 异步加载并拼接子世系数据 - 支持无限异步加载
    async loadAndAppendChildGenerations () {
      // 防止重复加载
      if (this.isLoadingChildren) {
        return
      }

      try {
        this.isLoadingChildren = true
        const { LoadAndAppendChildGenerations } = this

        // 使用当前节点id作为起点，固定请求3个世代的数据
        // 实现无限异步加载世系树的功能
        await LoadAndAppendChildGenerations({
          parentNodeId: this.node.id
        })

        // 加载成功后，更新节点的 has_child 状态
        // 如果加载后仍然没有子节点，说明确实没有子节点
        if (!this.node.children || this.node.children.length === 0) {
          this.node.has_child = 0
        } else {
          // 如果成功加载了子节点，确保has_child为1
          this.node.has_child = 1
        }

        // 同步展开状态到父组件
        this.syncExpandedState()
      } catch (error) {
        console.error('加载并拼接子世系数据失败:', error)
        throw error // 重新抛出错误，让调用方处理
      } finally {
        this.isLoadingChildren = false
      }
    },

    // 更新当前节点的真实ID，用于保存后的ID同步
    updateNodeWithRealId () {
      // 在父节点的子节点中查找与当前节点匹配的真实节点
      if (this.fatherNode && this.fatherNode.children) {
        const matchingNode = this.fatherNode.children.find(child =>
          child.name === this.node.name &&
          child.full_name === this.node.full_name &&
          child.ranking === this.node.ranking &&
          !child.id.toString().startsWith('temp_') // 确保是真实ID
        )

        if (matchingNode) {
          // 更新当前节点的ID和key
          this.node.id = matchingNode.id
          this.node.key = matchingNode.key
          // 移除临时标记
          delete this.node.isTemporary
          delete this.node.uniqueId
        }
      }
    },
    // 处理节点点击
    async handleNodeClick () {
      // 先发送点击事件
      this.$emit('click', this.node.id, this.node)

      // 检查是否有未展开的下级节点，如果有则自动展开
      await this.autoExpandIfHasChildren()
    },

    // 自动展开有子节点的节点 - 优化版本
    async autoExpandIfHasChildren () {
      // 检查节点是否可能有子节点
      const mightHaveChildren = this.couldHaveChildren()

      if (mightHaveChildren) {
        try {
          // 使用统一的判断逻辑检查是否需要加载子节点数据
          const needsLoading = this.shouldLoadChildren()

          if (needsLoading) {
            // 保存当前节点信息，用于加载后重新选中
            const currentNodeId = this.node.id
            const currentNode = this.node

            // 如果需要加载数据，先加载再展开
            await this.loadAndAppendChildGenerations()

            // 加载完成后检查是否成功获取到子节点数据
            const hasChildrenAfterLoading = this.node.children && this.node.children.length > 0

            if (hasChildrenAfterLoading) {
              // 加载成功，确保节点处于展开状态
              if (!this.expanded) {
                this.expanded = true
                this.hasBeenExpanded = true
                this.$emit('expand', this.node.key, this.expanded)
              }

              // 重新发送点击事件，确保当前节点保持选中状态
              // 使用 nextTick 确保数据更新完成后再发送事件
              this.$nextTick(() => {
                this.$emit('click', currentNodeId, currentNode)
              })
            }
          } else {
            // 如果不需要加载数据
            if (this.node.children && this.node.children.length > 0) {
              // 有子节点但未展开，直接展开
              if (!this.expanded) {
                this.expanded = true
                this.hasBeenExpanded = true
                this.$emit('expand', this.node.key, this.expanded)
              }
            }
          }
        } catch (error) {
          console.error('自动展开节点失败:', error)
          this.$message.error('自动展开节点失败，请手动点击展开')
        }
      }
    },
    // 处理配偶点击
    handleWifeClick (wife) {
      // 为配偶对象添加必要的属性，确保能被正确识别为配偶
      const wifeNode = {
        ...wife,
        husband_id: this.node.id, // 标识这是一个配偶节点
        generation: this.node.generation, // 继承丈夫的世代
        father_id: 0 // 配偶的father_id为0，因为配偶不是主支成员
      }
      this.$emit('wifeClick', wife.id, wifeNode)
    },
    getTagName (num) {
      return TagTypeMap[num]
    },
    async handleAddBrother () {
      if (!this.fatherNode) {
        this.$message.error('始祖节点不能添加兄弟节点')
        return
      }
      const { id, generation } = this.fatherNode
      const first_name = this.fatherNode.first_name
      if (!this.fatherNode.children) {
        this.fatherNode.children = []
      }
      const { children } = this.fatherNode
      const maxRank = this.fatherNode.children.length > 0 ? Math.max(...this.fatherNode.children.map(it => it.ranking)) + 1 : 1
      // 使用更安全的临时ID，避免与真实ID冲突
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      children.push(
        {
          'clan_id': this.currentSelectedClanId,
          'pedigree_id': this.currentSelectedPedigreeId,
          'id': tempId,
          'key': tempId,
          'first_name': first_name,
          'name': '',
          'sex': '男',
          'relation': '生',
          'children': [],
          'status': 0,
          'fatherId': id,
          'motherId': -1,
          'uniqueId': tempId,
          'generation': generation + 1,
          'ranking': maxRank,
          'writeMode': 1,
          'main': true,
          'limit': 0,
          'isTemporary': true // 标记为临时节点
        })
      if (children.length && !this.expanded) {
        this.expanded = !this.expanded
      }
      this.$emit('addBrother', this.node.id, this.node)
    },
    async handleAddChild () {
      // 确定父节点信息
      const parentNode = this.node
      let motherId = -1

      // 检查是否选中了配偶
      if (this.node.wife && this.node.wife.length > 0) {
        const selectedWife = this.node.wife.find(wife => wife.id === this.selectedKey)
        if (selectedWife) {
          // 选中的是配偶，设置母亲ID
          motherId = selectedWife.id
        }
      }

      const { id, generation } = parentNode
      const firstName = parentNode.first_name

      // 检查父节点ID是否为临时ID
      if (typeof id === 'string' && id.startsWith('temp_')) {
        this.$message.error('请先保存当前节点，再添加子节点')
        return
      }

      if (!parentNode.children) {
        parentNode.children = []
      }
      const { children } = parentNode
      const maxRank = children.length > 0 ? Math.max(...children.map(it => it.ranking)) + 1 : 1
      // 使用更安全的临时ID，避免与真实ID冲突
      const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      children.push(
        {
          'clan_id': this.currentSelectedClanId,
          'pedigree_id': this.currentSelectedPedigreeId,
          'id': tempId,
          'key': tempId,
          'first_name': firstName,
          'name': '',
          'sex': '男',
          'relation': '生',
          'children': [],
          'status': 0,
          'fatherId': id, // 这里的id已经确保不是临时ID
          'motherId': motherId, // 如果选中配偶则设置母亲ID
          'uniqueId': tempId,
          'generation': generation + 1,
          'ranking': maxRank,
          'ranking_text': getRankText(maxRank),
          'writeMode': 1,
          'main': true,
          'limit': 0,
          'living_status': 1,
          'isTemporary': true // 标记为临时节点
        })
      if (children.length && !this.expanded) {
        this.expanded = !this.expanded
      }
      this.$emit('addChild', parentNode.id, parentNode)
    },
    async handleSaveChildren () {
      const { AddMemberChild, SetLoading, ResetLoading } = this
      const { firstName, name, fullName } = getHandleName(this.childName.trim())
      // 调用保存孩子数据方法
      this.node.writeMode = 2
      this.node.name = name
      this.node.full_name = fullName
      this.node.ranking_text = getRankText(this.node.ranking)

      // 构建参数，排除新建时不需要的字段
      const {
        id,
        key,
        uniqueId,
        isTemporary,
        title,
        children,
        wife,
        ...cleanNodeData
      } = this.node

      // 获取真实的父节点ID
      let realFatherId = this.node.fatherId

      // 如果fatherId是临时ID，需要找到真实的父节点ID
      if (typeof realFatherId === 'string' && realFatherId.startsWith('temp_')) {
        // 从fatherNode获取真实ID，或者使用当前选中的节点ID
        if (this.fatherNode && this.fatherNode.id && !this.fatherNode.id.toString().startsWith('temp_')) {
          realFatherId = this.fatherNode.id
        } else {
          // 如果父节点也是临时的，这种情况需要先保存父节点
          this.$message.error('请先保存父节点，再添加子节点')
          return
        }
      }

      const params = {
        ...cleanNodeData,
        first_name: firstName,
        fatherId: realFatherId // 使用真实的父节点ID
      }
      SetLoading(true)

      try {
        const result = await AddMemberChild(params)
        if (result?.code === 0) {
          this.$message.success('添加成功')

          // 优化：使用父节点ID刷新新添加的子节点，保持当前树的展开状态
          try {
            // 获取真实的父节点ID
            const parentNodeId = realFatherId // 使用前面计算的真实父节点ID

            if (parentNodeId) {
              // 使用父节点ID刷新最新的子节点数据
              await this.RefreshParentNodeChildren({
                parentNodeId: parentNodeId
              })

              // 刷新后，当前节点的临时ID应该被替换为真实ID
              // 我们需要更新当前节点的引用，以便后续操作使用真实ID
              this.updateNodeWithRealId()

              // 确保父节点处于展开状态
              if (this.fatherNode && !this.fatherNode.expanded) {
                this.$emit('expand', this.fatherNode.key, true)
              }
            } else {
              // 如果没有父节点ID，回退到原来的刷新方式
              await this.RefreshPedigreeMemberListKeepExpanded()
            }
          } catch (refreshError) {
            console.error('刷新节点数据失败:', refreshError)
            this.$message.warning('添加成功，但刷新数据失败，请手动刷新页面')
          }

          // 由于接口不返回新节点数据，暂时不选中特定节点
          // 可以选中父节点或保持当前选中状态
        } else {
          this.$message.error(result?.message || '添加失败')
        }
      } catch (error) {
        console.error('添加节点失败:', error)
        this.$message.error('添加失败')
      } finally {
        ResetLoading()
      }
    },
    async handleSaveWife () {
      const { AddWife, wifeName, SetLoading, ResetLoading } = this
      const { id, generation } = this.node
      const wife = this.node.wife ? this.node.wife : []
      const wifeRank = wife.length > 0 ? Math.max(...wife.map(it => it.ranking)) + 1 : 1
      const { firstName, name, fullName } = getHandleName(wifeName.trim())

      // 配偶的性别应该与主成员的性别相反
      const spouseSex = this.node.sex === '女' ? '男' : '女'
      const spouseRankingText = spouseSex === '女' ? '长女' : '长子'

      const params = {
        'clan_id': this.currentSelectedClanId,
        'pedigree_id': this.currentSelectedPedigreeId,
        'generation': generation,
        'first_name': firstName,
        'name': name,
        'full_name': fullName,
        'ranking': wifeRank,
        'ranking_text': spouseRankingText,
        'relation': '配',
        'sex': spouseSex,
        'husband_id': id
      }
      SetLoading(true)

      try {
        const result = await AddWife(params)
        if (result?.code === 0) {
          this.$message.success('添加配偶成功')
          this.isAddWife = false
          this.wifeName = ''
          // 保持展开状态的刷新，重新获取最新的树数据
          try {
            await this.RefreshPedigreeMemberListKeepExpanded()
          } catch (refreshError) {
            console.error('刷新数据失败:', refreshError)
            this.$message.warning('添加配偶成功，但刷新数据失败，请手动刷新页面')
          }
          // 保持选中当前节点
          this.$emit('click', this.node.id, this.node)
        } else {
          this.$message.error(result?.message || '添加配偶失败')
        }
      } catch (error) {
        console.error('添加配偶失败:', error)
        this.$message.error('添加配偶失败')
      } finally {
        ResetLoading()
      }
    },
    handleDelChildren () {
      const { children } = this.fatherNode
      if (children.length === 1) {
        this.fatherNode.children = []
      } else {
        this.fatherNode.children = children.filter(item => item.id !== this.node.id)
      }
      // this.$emit('addChild', this.node.id, this.node)
    },
    getCurrentClass () {
      let result = ''
      // 默认样式
      result += 'node '
      // 展开样式
      if (this.node.children && this.expanded) {
        result += 'expand '
      }
      // 添加默认border
      result += 'border '
      // 没有子节点样式 - 修改逻辑以支持异步加载
      // 只有当节点明确没有子节点时才添加no-children类
      // 如果节点有children数组且长度为0，但可能还有未加载的子节点，则不添加no-children类
      const hasChildren = this.node.children && this.node.children.length > 0
      const mightHaveChildren = this.couldHaveChildren()

      if (!hasChildren && !mightHaveChildren) {
        result += 'no-children '
      }
      // 最后一个节点样式
      if (this.last) {
        result += 'last-item '
      }
      return result
    },
    couldHaveChildren () {
      // 判断节点是否可能有子节点
      // 优先使用has_child字段来判断，如果为null，则使用children数组的长度来判断
      if (this.node.has_child !== undefined && this.node.has_child !== null) {
        // 更健壮的判断，支持字符串、数字、布尔值
        const hasChild = this.node.has_child
        let result = false

        if (typeof hasChild === 'boolean') {
          result = hasChild
        } else if (typeof hasChild === 'string') {
          result = hasChild === '1' || hasChild.toLowerCase() === 'true'
        } else if (typeof hasChild === 'number') {
          result = hasChild === 1
        }

        return result
      }

      // 如果已经有子节点，直接返回true（不管has_child字段是否为null）
      if (this.node.children && this.node.children.length > 0) {
        return true
      }

      // 如果没有has_child字段或为null，并且没有子节点，默认返回false（不显示展开按钮）
      return false
    },
    getExpandIconClass () {
      // 优化版本：更准确地显示展开/折叠图标

      // 如果节点正在加载子数据，显示加载状态
      if (this.isLoadingChildren) {
        return 'icon-edit-toolbarjia loading' // 可以添加loading样式
      }

      // 检查节点是否有实际的子节点数据
      const hasActualChildren = this.node.children && this.node.children.length > 0

      // 如果节点已展开且有子节点，显示减号（折叠图标）
      if (this.expanded && hasActualChildren) {
        return 'icon-edit-toolbarjian'
      }

      // 根据has_child字段判断是否显示展开图标
      if (this.node.has_child === 1) {
        // has_child为1表示有可加载的子世代
        if (hasActualChildren && this.expanded) {
          // 如果已展开且有子节点，显示减号
          return 'icon-edit-toolbarjian'
        } else {
          // 如果未展开或没有子节点，显示加号
          return 'icon-edit-toolbarjia'
        }
      }

      // 如果has_child不为1，但已经有子节点数据，根据展开状态显示图标
      if (hasActualChildren) {
        return this.expanded ? 'icon-edit-toolbarjian' : 'icon-edit-toolbarjia'
      }

      // 其他情况不显示图标
      return ''
    },

    // 同步展开状态到父组件
    syncExpandedState () {
      // 确保父组件的expandedKeys与当前节点状态同步
      if (this.expanded && !this.expandedKeys.includes(this.node.key)) {
        this.$emit('expand', this.node.key, true)
      } else if (!this.expanded && this.expandedKeys.includes(this.node.key)) {
        this.$emit('expand', this.node.key, false)
      }
    },
    getStyleNumClass () {
      let result = ''
      if (this.last) {
        result += `style-${this.index === 0 ? '0' : '1'} `
      }
      if (this.selectedKey === this.node.fatherId && this.expanded && this.last) {
        result += 'style-2 '
      }
      return result
    },
    getItemClass () {
      let result = 'node border'
      if (this.last) {
        result += ' last-item'
      }
      if (this.node.children?.length === 0) {
        result += ' no-children'
      }
      return result
    },
    handleDelWife () {
      this.isAddWife = false
    },
    // 根据性别获取人物图标类名
    getPersonIconClass (sex) {
      // 如果是女性，使用wife-icon
      if (sex === '女') {
        return 'icon-edit-toolbarnvce wife-icon'
      }
      // 默认使用男性图标
      return 'icon-edit-toolbarnance'
    },
    // 根据主成员性别获取配偶图标类名
    getSpouseIconClass () {
      // 配偶的性别与主成员相反
      const spouseSex = this.node.sex === '女' ? '男' : '女'
      return this.getPersonIconClass(spouseSex)
    }
  }
}
</script>
<style scoped lang='less'>
.before {
  position: absolute;
  width: 80px;
  left: -92px;
}

.before,
.padding {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.switch {
  position: absolute;
  left: -7px;
  z-index: 98;
  flex-shrink: 0;
  margin-right: 5px;
  font-size: 14px;
  background-color: #fff;
  cursor: pointer;
}

.branch {
  color: #f14d4d;
}

.more-wife {
  border: 1px dashed #f5abab;
  color: #f5abab;
  background-color: #fff !important;
  line-height: 24px !important;
  padding: 0 10px;
  box-sizing: border-box;
  cursor: pointer;
}

.wife-item {
  cursor: pointer;
  display: flex;
  align-items: center;

  .wife-icon {
    color: #f5abab;
    font-size: 14px;
    flex-shrink: 0;
  }

  .wife-content {
    background-color: #f2f2f2;
    padding: 0 15px;
    margin-right: 4px;
    line-height: 26px;
    border-radius: 2px;
    min-width: 30px;
    height: 26px;
    display: flex;
    align-items: center;
    white-space: nowrap;

    .wife-text {
      font-size: 12px;
      color: #333;
      font-weight: 400;
    }
  }
}

.padding {
  width: 90px;
  text-align: right;

  .line {
    flex-grow: 1;
    width: 0;
    height: 0;
    border-top: 1px solid #f14d4d;
    position: relative;
  }

  .line.style-0 {
    border-top: none;
  }

  span {
    flex-shrink: 0;
  }
}

.selected-node {
  >div {
    >.node.last-item {
      >.children::after {
        content: '';
        position: absolute;
        top: 44px;
        border-left: 1px dashed #a1c1e6;
        height: calc(100% - 41px);
      }

      >.item:not(.children ~ .item) {
        >.padding::after {
          content: '';
          position: absolute;
          top: 35px;
          left: 0;
          border-left: 1px dashed #a1c1e6;
          height: 9px
        }
      }
    }
  }
}

.name {
  display: flex;
  align-items: center;
  position: relative;

  .cus-edit-toolbar-icon {
    font-size: 16px;
    color: #a1c1e6;
    flex-shrink: 0;
  }

  // 主成员的wife-icon样式
  .wife-icon {
    color: #f5abab;
    font-size: 14px;
    flex-shrink: 0;
  }

  .text {
    background-color: #f2f2f2;
    padding: 0 15px;
    margin-right: 4px;
    line-height: 26px;
    cursor: pointer;
    white-space: nowrap;
    border-radius: 2px;
    min-width: 30px;
    height: 26px;

    .btn-name {
      display: inline;
      padding: 0;
      margin: 0;
      background-color: transparent;
      border: none;
      outline: none;
      color: inherit;
      cursor: pointer;
    }
  }

  .name-top {
    position: absolute;
    left: 22px;
    top: -20px;
    height: 20px;
    line-height: 1;
    display: flex;
    align-items: center;
    width: -moz-fit-content;
    width: fit-content;

    .tag {
      display: block;
      flex-shrink: 0;
      transform: translateY(4px);
      width: 20px;
      height: 20px;
      margin-right: 4px;
      text-align: center;
      line-height: 20px;
      border-radius: 50%;
      border: 1px solid #f14d4d;
      color: #f14d4d;
      background-color: rgba(251, 224, 145, .7);
      font-size: 14px;
    }

    .branch {
      flex-shrink: 0;
      margin-right: 10px;
    }
  }

  .text.mode-1 {
    padding: 0 4px;
    background-color: #fff;

    input {
      width: 70px;
      margin-right: 8px;
    }

    i {
      font-size: 14px;
    }
  }
}

.name.selected .text {
  background-color: #f86e04;
  color: #fff;
}

/* 配偶选中状态样式 */
.name.wife-selected {
  .wife-content {
    background-color: #f86e04 !important;
    color: #fff;

    .wife-text {
      color: #fff !important;
    }
  }
}

.item {
  display: flex;
  align-items: center;
  padding-top: 20px;
  position: relative;
  white-space: nowrap;

  .before {
    transform: translateX(33px);
  }
}

.node {
  position: relative;
  margin-left: 96px;
}

.node.border {
  border-left: 1px solid #f14d4d;
}

.node.no-children>.item .switch {
  display: none;
}

.node.last-item {
  border-left: none !important;
}

.node.last-item .border-left.style-1:before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  left: 0;
  top: 0;
  height: 34px;
  border-left: 1px solid #f14d4d;
}

.node .border-left.style-1:before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  left: 0;
  top: 0;
  height: 34px;
  border-left: 1px solid #f14d4d;
}

.node .border-left.style-1.style-2:before {
  height: 100%;
  border-left: 1px dashed #a1c1e6;
}

.node .border-left.style-1.style-2:after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 33px;
  border-left: 1px solid #f14d4d;
}

.root>.node>.item .switch {
  left: 25px;
}

.more-child {
  padding-left: 96px;
  display: flex;
  color: #a1c1e6;
  cursor: pointer;

  i {
    margin-top: 20px;
  }

  .text {
    margin-top: 20px;
    height: 26px;
    line-height: 24px !important;
    border: 1px dashed #a1c1e6;
    padding: 0 15px;
    box-sizing: border-box;
    background-color: #fff !important;
  }
}

.more-child:before {
  content: "";
  display: block;
  width: 30px;
  height: 30px;
  border-left: 1px dashed #a1c1e6;
  border-bottom: 1px dashed #a1c1e6;
  margin-right: 0;
}

.more-brother:before {
  width: 0;
  height: 30px;
  margin: 0;
  transform: translateY(-13px);
}

.more-brother i,
.more-brother .text {
  transform: translateX(-11px);
}
</style>

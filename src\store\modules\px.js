import storage from 'store'
import {
  addPedigree,
  updatePedigree,
  deletedPedigree,
  getPedigreeList,
  add<PERSON><PERSON>tor,
  addMemberChild,
  deletedMember,
  addWife,
  getPedigreeMemberList,
  saveMemberInfo,
  getMemberDetail,
  getMemberList,
  moveMember,
  getTagTypeList,
  saveMemberBranch,
  deletedBranch
} from '@/api/px'
const px = {
  state: {
    list: [],
    quickOperationsType: '',
    currentSelectedPedigreeId: '',
    pedigreeMemberListLoading: false,
    currentPedigreeMemberList: null
  },
  mutations: {
    SET_List: (state, payload) => {
      state.list = payload
    },
    SET_CURRENT_SELECTED_PEDIGREE_ID: (state, payload) => {
      state.currentSelectedPedigreeId = payload
      storage.set('SET_CURRENT_SELECTED_PEDIGREE_ID', payload)
    },
    SET_PEDIGREE_MEMBER_LIST_LOADING: (state, payload) => {
      state.pedigreeMemberListLoading = payload
      storage.set('SET_PEDIGREE_MEMBER_LIST_LOADING', payload)
    },
    SET_CURRENT_PEDIGREE_MEMBER_LIST: (state, payload) => {
      state.currentPedigreeMemberList = payload
      storage.set('SET_CURRENT_PEDIGREE_MEMBER_LIST', payload)
    },
    SET_QUICK_OPERATIONS_TYPE: (state, payload) => {
      state.quickOperationsType = payload
    },
    APPEND_CHILDREN_TO_NODE: (state, { parentNodeId, newData }) => {
      // 递归查找父节点并添加子节点数据 - 优化版本
      const findAndAppendChildren = (nodes, targetId, newChildren) => {
        if (!nodes || !Array.isArray(nodes)) return false

        for (const node of nodes) {
          // 确保节点存在且有有效的id
          if (!node || node.id === undefined || node.id === null) {
            continue
          }

          if (node.id === targetId) {
            // 找到目标节点，初始化children数组
            if (!node.children) {
              node.children = []
            }

            // 处理新的子节点数据，支持无限异步拼接
            if (Array.isArray(newChildren) && newChildren.length > 0) {
              newChildren.forEach(child => {
                // 检查是否已存在相同ID的子节点，避免重复添加
                const existingChild = node.children.find(existing => existing.id === child.id)
                if (!existingChild) {
                  // 添加新的子节点，确保保留has_child字段
                  node.children.push({
                    ...child,
                    // 确保has_child字段被正确保留
                    has_child: child.has_child
                  })
                } else {
                  // 如果子节点已存在，但新数据有更多的children，则合并children
                  if (child.children && child.children.length > 0) {
                    if (!existingChild.children) {
                      existingChild.children = []
                    }
                    child.children.forEach(grandChild => {
                      if (!existingChild.children.find(existing => existing.id === grandChild.id)) {
                        existingChild.children.push({
                          ...grandChild,
                          // 确保孙子节点的has_child字段也被正确保留
                          has_child: grandChild.has_child
                        })
                      }
                    })
                  }

                  // 更新其他可能变化的字段，特别是has_child
                  if (child.has_child !== undefined) {
                    existingChild.has_child = child.has_child
                  }
                }
              })
            }

            // 不要自动修改父节点的has_child状态，保持后端返回的原始值

            return true
          }

          // 递归查找子节点
          if (node.children && findAndAppendChildren(node.children, targetId, newChildren)) {
            return true
          }
        }
        return false
      }

      // 处理数据拼接
      if (newData && Array.isArray(newData)) {
        // 确保 currentPedigreeMemberList 存在且不为空
        if (state.currentPedigreeMemberList) {
          const success = findAndAppendChildren([state.currentPedigreeMemberList], parentNodeId, newData)
          if (!success) {
            console.warn(`未找到ID为 ${parentNodeId} 的父节点，无法拼接子树数据`)
          }
        } else {
          console.warn('currentPedigreeMemberList 为空，无法拼接子树数据')
        }
      } else {
        console.warn('newData 不是有效的数组格式，无法拼接子树数据')
      }
    },

    REPLACE_PARENT_NODE_CHILDREN: (state, { parentNodeId, newChildrenData }) => {
      // 替换指定父节点的子节点数据，用于新增子节点后的局部刷新
      const findAndReplaceChildren = (nodes, targetId, newChildren) => {
        if (!nodes || !Array.isArray(nodes)) return false

        for (const node of nodes) {
          // 确保节点存在且有有效的id
          if (!node || node.id === undefined || node.id === null) {
            continue
          }

          if (node.id === targetId) {
            // 找到目标节点，替换其子节点数据
            node.children = newChildren || []

            // 更新父节点的has_child状态
            if (node.children.length > 0) {
              node.has_child = 1
            } else {
              node.has_child = 0
            }

            return true
          }

          // 递归查找子节点
          if (node.children && findAndReplaceChildren(node.children, targetId, newChildren)) {
            return true
          }
        }
        return false
      }

      // 处理数据替换
      if (newChildrenData && Array.isArray(newChildrenData)) {
        // 确保 currentPedigreeMemberList 存在且不为空
        if (state.currentPedigreeMemberList) {
          const success = findAndReplaceChildren([state.currentPedigreeMemberList], parentNodeId, newChildrenData)
          if (!success) {
            console.warn(`未找到ID为 ${parentNodeId} 的父节点，无法替换子节点数据`)
          }
        } else {
          console.warn('currentPedigreeMemberList 为空，无法替换子节点数据')
        }
      } else {
        console.warn('newChildrenData 不是有效的数组格式，无法替换子节点数据')
      }
    }
  },
  actions: {
    SetCurrentPedigreeId ({ commit }, payload) {
      commit('SET_CURRENT_SELECTED_PEDIGREE_ID', payload)
    },
    SetQuickOperationsType ({ commit }, payload) {
      commit('SET_QUICK_OPERATIONS_TYPE', payload)
    },
    ResetQuickOperationsType ({ commit }) {
      commit('SET_QUICK_OPERATIONS_TYPE', '')
    },
    SetLoading ({ commit }, payload) {
      commit('SET_PEDIGREE_MEMBER_LIST_LOADING', payload)
    },
    ResetLoading ({ commit }) {
      commit('SET_PEDIGREE_MEMBER_LIST_LOADING', false)
    },
    async AddPedigree ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addPedigree(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async UpdatePedigree ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        updatePedigree(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetPedigreeList ({ commit, state, rootState }) {
      return new Promise((resolve, reject) => {
        getPedigreeList({ clan_id: rootState.family.currentSelectedClanId })
          .then(json => {
            const list = json.data.list || []
            commit('SET_List', list)
            storage.set('SET_List', list)
            // 取浏览器中存储的pedigreeId
            const _existPedigreeId = localStorage.getItem('SET_CURRENT_SELECTED_PEDIGREE_ID')
            const obj = list.find(item => `${item.id}` === `${_existPedigreeId}`)
            if (_existPedigreeId && obj) {
              commit('SET_CURRENT_SELECTED_PEDIGREE_ID', Number(_existPedigreeId))
            } else {
              if (!state.currentSelectedPedigreeId && list?.length) {
                commit('SET_CURRENT_SELECTED_PEDIGREE_ID', list[0].id)
              }
              if (state.currentSelectedPedigreeId && list?.length) {
              const idList = list?.map(_it => _it.id)
              if (!idList.includes(state.currentSelectedPedigreeId)) {
                commit('SET_CURRENT_SELECTED_PEDIGREE_ID', list[0].id)
              }
            }
            }
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async DeletedPedigree ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        deletedPedigree(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async AddAncestor ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addAncestor(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
     },
    async AddMemberChild ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addMemberChild(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetNodeById ({ commit, state }, id) {
      return new Promise((resolve, reject) => {
        let result = null
        const _list = state.currentPedigreeMemberList ? [state.currentPedigreeMemberList] : []
        const getNodeById = (nodeList) => {
          nodeList.forEach(_item => {
            if (_item.id === id) {
              result = _item
              return false
            } else {
              getNodeById(_item.children || [])
            }
          })
        }
        _list.forEach(_item => {
          if (_item.id === id) {
            result = _item
          } else {
            getNodeById(_item.children || [])
          }
        })
        resolve(result)
      })
     },
    async AddWife ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        addWife(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async DeletedMember ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        deletedMember(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetPedigreeMemberList ({ commit }, payload) {
      return new Promise((resolve, reject) => {
        commit('SET_PEDIGREE_MEMBER_LIST_LOADING', true)
        getPedigreeMemberList(payload)
          .then(json => {
            commit('SET_PEDIGREE_MEMBER_LIST_LOADING', false)
            commit('SET_CURRENT_PEDIGREE_MEMBER_LIST', json.data)
            resolve(json)
          })
          .catch((e) => {
            commit('SET_PEDIGREE_MEMBER_LIST_LOADING', false)
            reject(e)
          })
      })
    },
    async RefreshPedigreeMemberList ({ commit, state, rootState, dispatch }, generation = 3) {
      try {
        const payload = {
          clan_id: rootState.family.currentSelectedClanId,
          pedigree_id: state.currentSelectedPedigreeId,
          generation: generation
        }
        const result = await dispatch('GetPedigreeMemberList', payload)
        return result
      } catch (error) {
        console.error('刷新数据失败:', error)
        throw error // 重新抛出错误，让调用方处理
      }
    },
    async RefreshPedigreeMemberListKeepExpanded ({ commit, state, rootState, dispatch }) {
      // 保持展开状态的刷新方法
      // 这个方法会触发数据刷新，但不会重置展开状态
      // 主要用于删除操作后的数据同步
      try {
        const payload = {
          clan_id: rootState.family.currentSelectedClanId,
          pedigree_id: state.currentSelectedPedigreeId,
          generation: 3,
          keepExpanded: true // 标识保持展开状态
        }
        const result = await dispatch('GetPedigreeMemberList', payload)
        return result
      } catch (error) {
        console.error('刷新数据失败:', error)
        throw error // 重新抛出错误，让调用方处理
      }
    },
    async LoadAndAppendChildGenerations ({ commit, state, rootState }, { parentNodeId }) {
      // 异步加载子世系数据，并拼接到现有树结构中
      // 支持无限异步加载世系树功能
      try {
        const payload = {
          clan_id: rootState.family.currentSelectedClanId,
          pedigree_id: state.currentSelectedPedigreeId,
          id: parentNodeId, // 使用当前子节点id作为起点
          generation: 3 // 固定请求3个世代的数据，支持无限异步拼接
        }

        // 直接调用API，不通过GetPedigreeMemberList action，避免替换整个树
        commit('SET_PEDIGREE_MEMBER_LIST_LOADING', true)
        const result = await getPedigreeMemberList(payload)
        commit('SET_PEDIGREE_MEMBER_LIST_LOADING', false)

        // 将请求回来的子树拼接到缓存的主世系树中
        if (result && result.data) {
          // 处理返回的数据结构
          let childrenData = []

          // 如果返回的是单个节点对象，提取其children
          if (result.data.children && Array.isArray(result.data.children)) {
            childrenData = result.data.children
          } else if (Array.isArray(result.data)) {
          // 如果返回的是数组，直接使用
            childrenData = result.data
          } else if (result.data.id) {
          // 如果返回的是单个节点且有id，将其作为子节点
            childrenData = [result.data]
          }

          // 过滤掉无效的子节点数据
          childrenData = childrenData.filter(child =>
            child &&
            (child.id !== undefined && child.id !== null)
          )

          // 对新加载的子节点数据进行 transformData 处理，与初始加载保持一致
          if (childrenData.length > 0) {
            // 应用 transformData 处理
            const transformData = (data) => {
              data.forEach(item => {
                item.title = item.full_name || ''
                item.key = item.id
                item.writeMode = 2
                if (item.children && item.children.length > 0) {
                  transformData(item.children)
                } else {
                  item.children = []
                }
              })
            }

            transformData(childrenData)

            // 替换父节点的子节点数据（而不是拼接）
            commit('REPLACE_PARENT_NODE_CHILDREN', {
              parentNodeId,
              newChildrenData: childrenData
            })
          } else {
            console.warn('没有有效的子节点数据可以拼接')
          }
        }

        return result
      } catch (error) {
        console.error('LoadAndAppendChildGenerations 失败:', error)
        throw error
      }
    },

    async RefreshParentNodeChildren ({ commit, state, rootState }, { parentNodeId }) {
      // 刷新指定父节点的子节点数据，用于新增子节点后的局部刷新
      try {
        const payload = {
          clan_id: rootState.family.currentSelectedClanId,
          pedigree_id: state.currentSelectedPedigreeId,
          id: parentNodeId, // 使用父节点id作为起点
          generation: 2 // 获取2个世代：父节点本身 + 子节点
        }

        // 直接调用API获取父节点的最新子节点数据
        commit('SET_PEDIGREE_MEMBER_LIST_LOADING', true)
        const result = await getPedigreeMemberList(payload)
        commit('SET_PEDIGREE_MEMBER_LIST_LOADING', false)

        // 处理返回的数据并更新指定父节点的子节点
        if (result && result.data) {
          let newChildrenData = []

          // 提取子节点数据
          if (result.data.children && Array.isArray(result.data.children)) {
            newChildrenData = result.data.children
          } else if (Array.isArray(result.data)) {
            newChildrenData = result.data
          }

          // 应用 transformData 处理
          if (newChildrenData.length > 0) {
            const transformData = (data) => {
              data.forEach(item => {
                item.title = item.full_name || ''
                item.key = item.id
                item.writeMode = 2
                if (item.children && item.children.length > 0) {
                  transformData(item.children)
                } else {
                  item.children = []
                }
              })
            }

            transformData(newChildrenData)

            // 替换父节点的子节点数据（而不是拼接）
            commit('REPLACE_PARENT_NODE_CHILDREN', {
              parentNodeId,
              newChildrenData
            })
          }
        }

        return result
      } catch (error) {
        console.error('RefreshParentNodeChildren 失败:', error)
        throw error
      }
    },
    async SaveMemberInfo ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        saveMemberInfo(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetMemberDetail ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        getMemberDetail(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetMemberList ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        getMemberList(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async MoveMember ({ commit, state, rootState }, payload) {
      return new Promise((resolve, reject) => {
        moveMember(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async GetTagTypeList ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        getTagTypeList(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async SaveMemberBranch ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        saveMemberBranch(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    },
    async DeletedBranch ({ commit, state }, payload) {
      return new Promise((resolve, reject) => {
        deletedBranch(payload)
          .then(json => {
            resolve(json)
          })
          .catch((e) => {
            reject(e)
          })
      })
    }
  }
}
export default px

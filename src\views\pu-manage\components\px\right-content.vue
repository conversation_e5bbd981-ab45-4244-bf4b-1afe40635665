<template>
  <div :class="getItemClass()">
    <!-- 成员操作菜单 -->
    <member-action-menu
      :showBase="showBase"
      :showBaseDetail="showBaseDetail"
      @toggleBase="showBaseInfo"
      @action="handleClick"
      @showModal="handleShowModal"
      @fenFang="handleFenFang" />
    <!-- 成员折叠表单 -->
    <member-base-info
      :showBase="showBase"
      :activeNodeInfo="activeNodeInfo"
      :selectedNode="selectedNode"
      :selectedNodeId="selectedNodeId"
      :father="father"
      :rawDetail="rawDetail"
      :loadingDetail.sync="loadingDetail"
      @toggleBaseMore="handleShowBaseMore"
      @refreshDetail="RefreshDetail"
      @updateTreeData="handleUpdateTreeData" />
    <!-- 成员展开表单 -->
    <member-detail-info
      :showBaseDetail="showBaseDetail"
      :mainNodeInfo="mainNodeInfo"
      :activeNodeInfo="activeNodeInfo"
      :father="father"
      :rawDetail="rawDetail"
      @toggleDetail="handleShowBaseMore"
      @selectedNode="handleSelectedNode"
      @addWife="handleAddWife"
      @deleteWife="handleDeleteWife"
      @refresh="RefreshDetail"
      @updateTreeData="handleUpdateTreeData"
      @updateTreeSelection="handleUpdateTreeSelection" />
    <!-- 始祖弹窗 -->
    <add-ancestor v-if="showAddAncestor" @close="() => showAddAncestor = false" />
    <!-- 过继 -->
    <guoji-modal v-if="showGuojiModal" :nodeInfo="nodeInfo" @close="() => showGuojiModal = false" />
    <!-- 分房 -->
    <fen-fang-modal
      v-if="showFenFangModal"
      :tag="selectTagType"
      :nodeInfo="nodeInfo"
      @close="() => showFenFangModal = false" />
    <!-- 设庄 -->
    <shezhuang-modal v-if="showSheZhuangModal" :selectedNode="selectedNode" @close="() => showSheZhuangModal = false" />
    <!-- 标记 -->
    <biao-ji-modal v-if="showBiaoJiModal" :selectedNode="selectedNode" @close="showBiaoJiModal = false" />
    <!-- 统计 -->
    <tongji-modal v-if="showTongjiModal" @close="showTongjiModal = false" />
    <!-- 造字 -->
    <zao-zi-modal v-if="showZaoziModal" @close="showZaoziModal = false" />
    <!-- 绑定用户 -->
    <bind-modal v-if="showBindModal" @close="() => showBindModal = false" />
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import cloneDeep from 'lodash.clonedeep'
import * as actionTypes from '@/store/modules/px-action-type'
import AddAncestor from '@/views/pu-manage/components/px/components/add-ancestor'
import GuojiModal from '@/views/pu-manage/components/px/components/guoji-modal'
import FenFangModal from '@/views/pu-manage/components/px/components/fenfang-modal'
import ShezhuangModal from '@/views/pu-manage/components/px/components/shezhuang-modal'
import BiaoJiModal from '@/views/pu-manage/components/px/components/biaoji-modal'
import TongjiModal from '@/views/pu-manage/components/px/components/tongji-modal'
import ZaoZiModal from '@/views/pu-manage/components/px/components/zaozi-modal'

import BindModal from '@/views/pu-manage/components/px/components/bind-modal'
import WifeChildrenInfo from '@/views/pu-manage/components/px/components/wife-children-info'

import MemberBaseInfo from '@/views/pu-manage/components/px/components/MemberBaseInfo'
import MemberDetailInfo from '@/views/pu-manage/components/px/components/MemberDetailInfo'
import MemberActionMenu from '@/views/pu-manage/components/px/components/MemberActionMenu'
export default {
  name: 'PXRightContent',
  components: {
    WifeChildrenInfo,
    BindModal,
    ZaoZiModal,
    TongjiModal,
    BiaoJiModal,
    ShezhuangModal,
    FenFangModal,
    GuojiModal,
    AddAncestor,
    MemberBaseInfo,
    MemberDetailInfo,
    MemberActionMenu
  },
  data () {
    return {
      loadingDetail: false,
      nodeInfo: {}, // 主支成员信息
      activeNodeInfo: {}, // 当前激活的节点信息（可能是主节点或配偶）
      mainNodeInfo: {}, // 始终保存主支成员的完整信息，用于DetailTabsHeader
      rawDetail: {}, // 获取到的详情信息

      showBase: true,
      showBaseDetail: false,
      showAddAncestor: false,
      showGuojiModal: false,
      showFenFangModal: false,
      showSheZhuangModal: false,
      showBiaoJiModal: false,
      showTongjiModal: false,
      showZaoziModal: false,
      showBindModal: false,
      actionTypes,
      selectTagType: '',
      refreshDetailTimer: null // 防抖定时器
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  props: {
    selectedNode: {
      type: Object,
      default: () => { }
    },
    selectedNodeId: {
      type: [String, Number],
      default: ''
    },
    fatherId: {
      type: [Number, String],
      default: 0
    },
    father: {
      type: Object,
      default: () => { }
    }
  },
  watch: {
    selectedNode: {
      handler (val) {
        if (val) {
          // 判断是否是配偶节点
          if (val.husband_id) {
            // 这是一个配偶节点
            // mainNodeInfo从世系树获取（不参与表单回显）
            this.setMainNodeInfoFromTree(val.husband_id)
            // activeNodeInfo通过详情接口获取（用于表单回显）
            this.GetWifeDetail(val.id, 0)
          } else {
            // 这是主节点
            // mainNodeInfo从世系树获取（不参与表单回显）
            this.mainNodeInfo = cloneDeep(val)
            // activeNodeInfo通过详情接口获取（用于表单回显）
            this.GetDetail(val.id, val.father_id || 0)
          }
        } else {
          // 如果没有选中节点，重置状态
          this.nodeInfo = {}
          this.activeNodeInfo = {}
          this.mainNodeInfo = {}
          this.rawDetail = {}
        }
      },
      immediate: true,
      deep: true
    },
    father: {
      handler () {
        // father 信息已经通过 props 传递给 MemberBaseInfo 组件处理
      },
      immediate: true,
      deep: true
    },
    activeNodeInfo: {
      handler () {
        // activeNodeInfo 的变化现在由子组件 MemberBaseInfo 和 MemberDetailInfo 自己监听处理
        // 这里暂时保留 watcher 结构，以防将来需要添加其他逻辑
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    ...mapActions(['RefreshPedigreeMemberList', 'RefreshPedigreeMemberListKeepExpanded', 'SetQuickOperationsType', 'GetMemberDetail', 'DeletedMember', 'GetNodeById']),

    showBaseInfo () {
      if (this.showBase) {
        this.lastShowBase = this.showBase
      }
      if (this.showBaseDetail) {
        this.lastShowBaseDetail = this.showBaseDetail
      }
      if (this.showBaseDetail || this.showBase) {
        this.showBase = false
        this.showBaseDetail = false
      } else {
        if (this.lastShowBase) {
          this.showBase = true
          this.lastShowBase = false
        }
        if (this.lastShowBaseDetail) {
          this.showBaseDetail = true
          this.lastShowBaseDetail = false
        }
      }
    },
    handleShowBaseMore () {
      this.showBaseDetail = !this.showBaseDetail
      this.showBase = !this.showBase
    },

    async GetDetail (nodeId, fatherId) {
      const { GetMemberDetail } = this
      const params = {
        id: nodeId,
        father_id: fatherId,
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId
      }
      this.rawDetail = { show_text: '', fast_text: '' }
      this.loadingDetail = true
      const result = await GetMemberDetail(params)
      this.loadingDetail = false
      if (result?.code === 0) {
        this.rawDetail = result.data || { show_text: '', fast_text: '' }
        if (result.data?.info?.wife?.length > 0) {
          // 如果 info 是一个对象
          result.data.info.wife = result.data.info.wife.map(item => ({ ...item, key: item.id }))
        }
        if (result.data?.info?.children?.length > 0) {
          // 如果 info 是一个对象
          result.data.info.children = result.data.info.children.map(item => ({ ...item, key: item.id }))
        }
        this.nodeInfo = result.data?.info
        // activeNodeInfo用于表单回显，从详情接口获取完整数据
        this.activeNodeInfo = result.data?.info
        // 数据已经存储在 this.rawDetail、this.nodeInfo 和 this.activeNodeInfo 中
      }
    },
    async RefreshDetail () {
      // 防抖处理，避免短时间内多次调用详情接口
      if (this.refreshDetailTimer) {
        clearTimeout(this.refreshDetailTimer)
      }

      this.refreshDetailTimer = setTimeout(async () => {
        // 根据当前激活的节点类型来刷新数据
        if (this.activeNodeInfo?.husband_id) {
          // 当前激活的是配偶 - fatherId为0
          await this.GetWifeDetail(this.activeNodeInfo.id, 0)
        } else {
          // 当前激活的是主节点
          const params = {
            id: this.selectedNode.id,
            fatherId: this.selectedNode.father_id || 0
          }
          await this.GetDetail(params.id, params.fatherId)
        }
        this.refreshDetailTimer = null
      }, 100) // 100ms 防抖延迟
    },
    getItemClass () {
      if (this.showBase) {
        return 'cus-right-content open-base'
      }
      if (this.showBaseDetail) {
        return 'cus-right-content open-base-detail'
      }
      return 'cus-right-content'
    },
    handleClick (type) {
      const { SetQuickOperationsType } = this
      switch (type) {
        case actionTypes.PX_RIGHT_OPTION_ADD_WIFE:
        case actionTypes.PX_RIGHT_OPTION_ADD_CHILD:
        case actionTypes.PX_RIGHT_OPTION_ADD_BROTHER:
          SetQuickOperationsType(type)
          break
        case actionTypes.PX_RIGHT_OPTION_ADD_ANCESTOR:
          this.showAddAncestor = true
          break
        case actionTypes.PX_RIGHT_OPTION_DELETE_NODE:
          this.$emit('deleteNode')
          break
      }
    },
    handleFenFang (type) {
      this.selectTagType = type
      this.showFenFangModal = true
    },
    handleShowModal (modalType) {
      switch (modalType) {
        case 'guoji':
          this.showGuojiModal = true
          break
        case 'biaoji':
          this.showBiaoJiModal = true
          break
        case 'tongji':
          this.showTongjiModal = true
          break
        case 'shezhuang':
          this.showSheZhuangModal = true
          break
        case 'zaozi':
          this.showZaoziModal = true
          break
      }
    },
    handleSelectedNode (node) {
      // 处理配偶或主节点的选择
      this.activeNodeInfo = cloneDeep(node)

      // 如果选择的是配偶，需要获取配偶的详细信息
      if (node.husband_id) {
        // 这是一个配偶节点 - fatherId为0
        this.GetWifeDetail(node.id, 0)
      } else {
        // 这是主节点，使用原有逻辑
        this.GetDetail(node.id, node.father_id || 0)
        // 如果选择的是主节点，更新mainNodeInfo
        this.mainNodeInfo = cloneDeep(node)
      }

      // 通知父组件更新世系树的选中状态
      this.$emit('updateTreeSelection', node)

      // 向父组件发送事件
      this.$emit('handleSelectedNode', node.id, node)
    },
    async handleAddWife () {
      // 触发添加配偶的操作
      this.handleClick(this.actionTypes.PX_RIGHT_OPTION_ADD_WIFE)
    },
    async handleDeleteWife (wife) {
      // 删除配偶的逻辑
      this.$confirm({
        title: '确认删除',
        content: `确定要删除配偶 ${wife.full_name || wife.name} 吗？`,
        onOk: async () => {
          try {
            const params = {
              clan_id: this.currentSelectedClanId,
              pedigree_id: this.currentSelectedPedigreeId,
              id: wife.id
            }
            // 调用删除成员的API（配偶也是成员）
            const res = await this.DeletedMember(params)
            if (res.code === 0) {
              this.$message.success('删除成功')
              // 保持展开状态的刷新
              try {
                await this.RefreshPedigreeMemberListKeepExpanded()
              } catch (refreshError) {
                console.error('刷新数据失败:', refreshError)
                this.$message.warning('删除成功，但刷新数据失败，请手动刷新页面')
              }
              // 如果删除的是当前激活的配偶，切换回主节点
              if (this.activeNodeInfo.id === wife.id) {
                this.activeNodeInfo = cloneDeep(this.nodeInfo)
                this.GetDetail(this.nodeInfo.id, this.nodeInfo.father_id || 0)
              }
            } else {
              this.$message.error(res.message || '删除失败')
            }
          } catch (error) {
            this.$message.error('删除失败')
            console.error('删除配偶失败:', error)
          }
        }
      })
    },
    async GetWifeDetail (wifeId, fatherId = 0) {
      // 获取配偶的详细信息
      const { GetMemberDetail } = this
      const params = {
        id: wifeId,
        father_id: fatherId || 0, // 配偶的father_id为0
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId
      }
      this.rawDetail = { show_text: '', fast_text: '' }
      this.loadingDetail = true
      try {
        const result = await GetMemberDetail(params)
        this.loadingDetail = false
        if (result?.code === 0) {
          this.rawDetail = result.data || { show_text: '', fast_text: '' }
          // 更新activeNodeInfo为配偶的详细信息
          this.activeNodeInfo = result.data?.info || this.activeNodeInfo
          // 数据已经存储在 this.rawDetail 和 this.activeNodeInfo 中，不需要再更新表单字段
        }
      } catch (error) {
        this.loadingDetail = false
        console.error('获取配偶详情失败:', error)
      }
    },
    async setMainNodeInfoFromTree (mainNodeId) {
      // 从世系树获取主支成员信息，用于显示标签页等（不参与表单回显）
      try {
        const mainNode = await this.GetNodeById(mainNodeId)
        if (mainNode) {
          // mainNodeInfo直接使用树数据，不参与表单回显
          this.mainNodeInfo = cloneDeep(mainNode)
        } else {
          console.warn('在世系树中未找到主支成员，ID:', mainNodeId)
          // 如果在树数据中找不到，回退到API请求
          await this.GetMainNodeInfo(mainNodeId)
        }
      } catch (error) {
        console.error('从世系树获取主支成员信息失败:', error)
        // 出错时回退到API请求
        await this.GetMainNodeInfo(mainNodeId)
      }
    },

    async GetMainNodeInfo (mainNodeId) {
      // 获取主支成员信息，用于显示标签页（备用方法）
      try {
        // 首先从当前树数据中查找主支成员
        const mainNode = await this.GetNodeById(mainNodeId)
        if (mainNode) {
          // mainNodeInfo直接使用树数据，不参与表单回显
          this.mainNodeInfo = cloneDeep(mainNode)
        }
      } catch (error) {
        console.error('获取主支成员信息失败:', error)
      }
    },

    handleUpdateTreeData (updateInfo) {
      // 处理MemberBaseInfo和MemberDetailInfo更新后同步数据
      const { nodeId, updatedData, fieldType } = updateInfo

      // 通知父组件（PX.vue）更新世系树数据
      this.$emit('updateTreeNodeData', {
        nodeId,
        updatedData,
        fieldType
      })

      // 同步更新本地数据，确保两个组件之间的数据一致性
      this.updateLocalData(nodeId, updatedData, fieldType)
    },

    updateLocalData (nodeId, updatedData, fieldType) {
      // 更新本地数据，确保MemberBaseInfo和MemberDetailInfo之间的数据同步

      // 如果更新的是当前激活的节点
      if (this.activeNodeInfo && this.activeNodeInfo.id === nodeId) {
        // 更新activeNodeInfo
        const updatedActiveNodeInfo = { ...this.activeNodeInfo }
        Object.keys(updatedData).forEach(key => {
          if (key !== 'clan_id' && key !== 'pedigree_id' && key !== 'id') {
            // 字段映射
            let nodeKey = key
            if (key === 'rankingText') {
              nodeKey = 'ranking_text'
            }
            updatedActiveNodeInfo[nodeKey] = updatedData[key]
          }
        })

        // 特殊处理：如果更新了姓名相关字段，需要更新显示名称
        if (updatedData.first_name || updatedData.name) {
          const firstName = updatedData.first_name || updatedActiveNodeInfo.first_name || ''
          const name = updatedData.name || updatedActiveNodeInfo.name || ''
          updatedActiveNodeInfo.full_name = firstName + name
        }

        this.activeNodeInfo = updatedActiveNodeInfo
      }

      // 如果更新的是当前选中的节点
      if (this.selectedNode && this.selectedNode.id === nodeId) {
        // 更新selectedNode
        const updatedSelectedNode = { ...this.selectedNode }
        Object.keys(updatedData).forEach(key => {
          if (key !== 'clan_id' && key !== 'pedigree_id' && key !== 'id') {
            let nodeKey = key
            if (key === 'rankingText') {
              nodeKey = 'ranking_text'
            }
            updatedSelectedNode[nodeKey] = updatedData[key]
          }
        })

        if (updatedData.first_name || updatedData.name) {
          const firstName = updatedData.first_name || updatedSelectedNode.first_name || ''
          const name = updatedData.name || updatedSelectedNode.name || ''
          updatedSelectedNode.full_name = firstName + name
        }

        this.selectedNode = updatedSelectedNode
      }

      // 更新rawDetail中的info数据
      if (this.rawDetail && this.rawDetail.info && this.rawDetail.info.id === nodeId) {
        const updatedInfo = { ...this.rawDetail.info }
        Object.keys(updatedData).forEach(key => {
          if (key !== 'clan_id' && key !== 'pedigree_id' && key !== 'id') {
            let infoKey = key
            if (key === 'rankingText') {
              infoKey = 'ranking_text'
            }
            updatedInfo[infoKey] = updatedData[key]
          }
        })

        if (updatedData.first_name || updatedData.name) {
          const firstName = updatedData.first_name || updatedInfo.first_name || ''
          const name = updatedData.name || updatedInfo.name || ''
          updatedInfo.full_name = firstName + name
        }

        this.rawDetail = {
          ...this.rawDetail,
          info: updatedInfo
        }
      }
    },

    handleUpdateTreeSelection (node) {
      // 处理Tab1BaseInfo组件选中配偶时，同步更新世系树的选中状态
      this.$emit('updateTreeSelection', node)
    }
  },
  beforeDestroy () {
    // 清理防抖定时器
    if (this.refreshDetailTimer) {
      clearTimeout(this.refreshDetailTimer)
      this.refreshDetailTimer = null
    }
  }
}
</script>
<style lang='less' scoped>
.cus-right-content {
  padding-left: 4px;
  height: calc(100vh - 60px);
  flex-shrink: 0;
  margin-left: 10px;
  background-color: #f5f5f5;
  overflow-x: hidden;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-left: 10px;

}

.open-base {
  width: 300px;
}

.open-base-detail {
  max-width: 900px;
}

/deep/ .select-no-border {
  .ant-select-selection {
    border: none;
  }
}

/deep/ .select-left-border {
  .ant-select-selection {
    border-left: 1px solid #d9d9d9;
  }
}
</style>
